<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON><PERSON><PERSON> - Creative Portfolio</title>
  <link rel="stylesheet" href="style.css" />
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Professional Splash Screen -->
  <div id="splash-screen" class="splash-screen">
    <!-- Water Fill Effect -->
    <div class="water-container">
      <div class="water-fill"></div>
      <div class="water-surface"></div>
    </div>

    <!-- Bubbles Container -->
    <div class="bubbles-container"></div>

    <!-- Water Splash Effects -->
    <div class="water-splash"></div>

    <!-- Screen Break Effect -->
    <div class="screen-break">
      <div class="crack crack-1"></div>
      <div class="crack crack-2"></div>
      <div class="crack crack-3"></div>
      <div class="crack crack-4"></div>
      <div class="crack crack-5"></div>
      <div class="crack crack-6"></div>
      <div class="crack crack-7"></div>
    </div>

    <!-- Main Content -->
    <div class="splash-content">
      <div class="logo-container">
        <img src="images/logo.png" alt="Saksham Godiyal" class="splash-logo">
      </div>
      <h1 class="splash-name">Saksham Godiyal</h1>
      <p class="splash-subtitle">Click to Enter</p>
    </div>
  </div>

  <!-- Main Website Content -->
  <div id="main-content" class="main-content hidden">

  <!-- Parallax Background -->
  <div class="parallax-bg">
    <div class="parallax-element"></div>
    <div class="parallax-element"></div>
    <div class="parallax-element"></div>
    <div class="parallax-element"></div>
    <div class="parallax-element"></div>
  </div>

  <!-- Interactive Scroll Robot -->
  <div class="interactive-robot">
    <div class="robot-body">
      <i class="fas fa-user-astronaut"></i>
    </div>
    <div class="robot-speech-bubble">
      <span class="speech-text">Hello! 👋</span>
    </div>
    <div class="robot-attention-pointer"></div>
  </div>

  <!-- Robot Footprints Container -->
  <div class="robot-footprints-container"></div>

  <!-- Enhanced Navigation Header -->
  <nav class="navbar">
    <div class="nav-container">
      <!-- Enhanced Logo Section -->
      <div class="nav-brand">
        <div class="logo-container-nav">
          <img src="images/logo.png" alt="Saksham Godiyal" class="logo-img">
          <div class="logo-glow-ring"></div>
        </div>
        <div class="brand-info">
          <span class="logo-text">Saksham Godiyal</span>
          <span class="brand-tagline">Full Stack Developer</span>
        </div>
      </div>

      <!-- Status Indicators -->
      <div class="status-indicators">
        <div class="status-item">
          <div class="status-dot available"></div>
          <span class="status-text">Available for Work</span>
        </div>
        <div class="tech-stats">
          <div class="stat-item">
            <span class="stat-number">BTech</span>
            <span class="stat-label">3rd Year</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">15+</span>
            <span class="stat-label">Projects</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">5+</span>
            <span class="stat-label">Hackathons</span>
          </div>
        </div>
      </div>

      <!-- Navigation Menu -->
      <ul class="nav-menu">
        <li class="nav-item">
          <a href="#home" class="nav-link">Home</a>
        </li>
        <li class="nav-item">
          <a href="#about" class="nav-link">About</a>
        </li>
        <li class="nav-item">
          <a href="#skills" class="nav-link">Skills</a>
        </li>
        <li class="nav-item">
          <a href="#projects" class="nav-link">Projects</a>
        </li>
        <li class="nav-item">
          <a href="#contact" class="nav-link">Contact</a>
        </li>
      </ul>

      <!-- Quick Actions -->
      <div class="quick-actions">
        <a href="#contact" class="btn-hire">Hire Me</a>
        <div class="theme-toggle">
          <div class="toggle-icon">⚡</div>
        </div>
      </div>

      <div class="hamburger">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="hero">
    <div class="hero-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          Hi, I'm <span class="highlight">Saksham Godiyal</span>
        </h1>
        <p class="hero-subtitle">
          <span class="typing-text"></span>
        </p>
        <p class="hero-description">
          A dynamic B.Tech student specializing in Computer Science and Engineering, driven by a passion for technology and determination to make a meaningful impact in the tech industry.
        </p>
        <div class="hero-buttons">
          <a href="#about" class="btn btn-primary">Learn More</a>
          <a href="#contact" class="btn btn-secondary">Get In Touch</a>
        </div>
      </div>
      <div class="hero-image">
        <div class="profile-container">
          <img src="images/profile-pic.png" alt="Saksham Godiyal" class="profile-pic" />
          <div class="profile-ring"></div>
        </div>
      </div>
    </div>
    <div class="scroll-indicator">
      <div class="scroll-arrow">
        <i class="fas fa-chevron-down"></i>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">About Me</h2>
        <p class="section-subtitle">Get to know me better</p>
      </div>
      <div class="about-content">
        <div class="about-text">
          <h3>Hello! I'm Saksham Godiyal</h3>
          <p>
            Meet Saksham Godiyal, a dynamic B.Tech student specializing in Computer Science and Engineering,
            driven by a passion for technology and a determination to make a meaningful impact in the tech
            industry through innovative skills and boundless enthusiasm.
          </p>
          <p>
            I have established a solid foundation in Python and am currently enhancing my programming skill set
            by exploring C, C++ and Java. Additionally, I have developed working proficiency in C# and have
            actively pursued game development experience through extensive use of Unity Hub.
          </p>
          <div class="about-stats">
            <div class="stat interactive-stat" data-popup="projects">
              <h4>15+</h4>
              <p>Programming Projects</p>
              <div class="stat-popup projects-popup">
                <div class="popup-header">My Programming Projects</div>
                <div class="popup-content">
                  <div class="popup-item">Portfolio Website</div>
                  <div class="popup-item">Disaster Evacuation HyTech System</div>
                  <div class="popup-item">Hand Gesture Security System</div>
                  <div class="popup-item">Agriculture Data Analysis System</div>
                  <div class="popup-item">2D Games</div>
                  <div class="popup-item">Text to Speech System</div>
                  <div class="popup-item">Speech to Text System</div>
                  <div class="popup-item">Voice Control System</div>
                </div>
              </div>
            </div>
            <div class="stat interactive-stat" data-popup="events">
              <h4>5+</h4>
              <p>Major Events Managed</p>
              <div class="stat-popup events-popup">
                <div class="popup-header">Events I've Managed</div>
                <div class="popup-content">
                  <div class="popup-item">National Games 2025</div>
                  <div class="popup-item">Grafest 2024</div>
                  <div class="popup-item">Induction 2024</div>
                  <div class="popup-item">Convocation 2024</div>
                  <div class="popup-item">Fresher 2024</div>
                </div>
              </div>
            </div>
            <div class="stat interactive-stat" data-popup="languages">
              <h4>8+</h4>
              <p>Programming Languages</p>
              <div class="stat-popup languages-popup">
                <div class="popup-header">Programming Languages</div>
                <div class="popup-content">
                  <div class="popup-item">C</div>
                  <div class="popup-item">C++</div>
                  <div class="popup-item">Python</div>
                  <div class="popup-item">Java</div>
                  <div class="popup-item">HTML</div>
                  <div class="popup-item">JavaScript</div>
                  <div class="popup-item">CSS</div>
                  <div class="popup-item">C#</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="about-image">
          <div class="about-img-container">
            <img src="images/about-me.jpg" alt="Saksham Godiyal - About Me" />
            <div class="about-overlay"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Skills Section -->
  <section id="skills" class="skills">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">My Skills</h2>
        <p class="section-subtitle">What I bring to the table</p>
      </div>
      <div class="skills-content">
        <div class="skills-categories">
          <div class="skill-category">
            <div class="category-icon">
              <i class="fas fa-code"></i>
            </div>
            <h3>Programming</h3>
            <div class="skill-bars">
              <div class="skill-bar">
                <div class="skill-info">
                  <span>Python</span>
                  <span>90%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="90"></div>
                </div>
              </div>
              <div class="skill-bar">
                <div class="skill-info">
                  <span>C++</span>
                  <span>80%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="80"></div>
                </div>
              </div>
              <div class="skill-bar">
                <div class="skill-info">
                  <span>Java</span>
                  <span>75%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="75"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="skill-category">
            <div class="category-icon">
              <i class="fas fa-gamepad"></i>
            </div>
            <h3>Game Development & Others</h3>
            <div class="skill-bars">
              <div class="skill-bar">
                <div class="skill-info">
                  <span>Unity Hub</span>
                  <span>85%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="85"></div>
                </div>
              </div>
              <div class="skill-bar">
                <div class="skill-info">
                  <span>C#</span>
                  <span>80%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="80"></div>
                </div>
              </div>
              <div class="skill-bar">
                <div class="skill-info">
                  <span>Event Management</span>
                  <span>95%</span>
                </div>
                <div class="skill-progress">
                  <div class="skill-fill" data-width="95"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="projects">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">My Projects</h2>
        <p class="section-subtitle">Some of my recent work</p>
      </div>
      <div class="projects-grid">
        <div class="project-card">
          <div class="project-image">
            <img src="images/unity-project.jpg" alt="Unity Game Development" />
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-info">
            <h3>Unity Game Development</h3>
            <p>Interactive game projects developed using Unity Hub and C# programming</p>
            <div class="project-tech">
              <span class="tech-tag">Unity</span>
              <span class="tech-tag">C#</span>
              <span class="tech-tag">Game Design</span>
            </div>
          </div>
        </div>
        <div class="project-card">
          <div class="project-image">
            <img src="images/event-management.jpg" alt="Event Management" />
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-info">
            <h3>Event Management Projects</h3>
            <p>Led event management for National Games 2025, Decathlon 5th Anniversary, and university fests</p>
            <div class="project-tech">
              <span class="tech-tag">Leadership</span>
              <span class="tech-tag">Planning</span>
              <span class="tech-tag">Coordination</span>
            </div>
          </div>
        </div>
        <div class="project-card">
          <div class="project-image">
            <img src="images/programming-projects.jpg" alt="Programming Projects" />
            <div class="project-overlay">
              <div class="project-links">
                <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i></a>
                <a href="#" class="project-link"><i class="fab fa-github"></i></a>
              </div>
            </div>
          </div>
          <div class="project-info">
            <h3>Programming Assignments</h3>
            <p>Various coding projects and assignments showcasing skills in Python, C++, and Java</p>
            <div class="project-tech">
              <span class="tech-tag">Python</span>
              <span class="tech-tag">C++</span>
              <span class="tech-tag">Java</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Get In Touch</h2>
        <p class="section-subtitle">Let's work together</p>
      </div>
      <div class="contact-content">
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <div class="contact-details">
              <h4>Email</h4>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fab fa-linkedin"></i>
            </div>
            <div class="contact-details">
              <h4>LinkedIn</h4>
              <p><a href="https://www.linkedin.com/in/saksham-godiyal-614291305" target="_blank">Saksham Godiyal</a></p>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">
              <i class="fab fa-instagram"></i>
            </div>
            <div class="contact-details">
              <h4>Instagram</h4>
              <p><a href="https://www.instagram.com/saksham.sg12" target="_blank">@saksham.sg12</a></p>
            </div>
          </div>
        </div>
        <div class="contact-form">
          <form class="form">
            <div class="form-group">
              <input type="text" class="form-input" placeholder="Your Name" required>
            </div>
            <div class="form-group">
              <input type="email" class="form-input" placeholder="Your Email" required>
            </div>
            <div class="form-group">
              <textarea class="form-input" rows="5" placeholder="Your Message" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">Send Message</button>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <p>&copy; 2025 Saksham Godiyal. All rights reserved.</p>
        <div class="social-links">
          <a href="https://youtube.com/shorts/CLeldFEJ5FU?feature=share" class="social-link" target="_blank"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/saksham-godiyal-614291305" class="social-link" target="_blank"><i class="fab fa-linkedin"></i></a>
          <a href="mailto:<EMAIL>" class="social-link"><i class="fas fa-envelope"></i></a>
          <a href="https://www.instagram.com/saksham.sg12" class="social-link" target="_blank"><i class="fab fa-instagram"></i></a>
        </div>
      </div>
    </div>
  </footer>
  </div> <!-- End Main Content -->

  <script src="script.js"></script>
</body>
</html>
