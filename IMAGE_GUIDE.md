# 🖼️ How to Add Pictures & Logo to Your Website

## 📁 **Image File Structure**
Your website now expects images in the `images/` folder:

```
website/
├── images/
│   ├── logo.png              (Your logo - 40x40px recommended)
│   ├── profile-pic.jpg       (Your main profile photo - 300x300px)
│   ├── about-me.jpg          (About section photo - 400x500px)
│   ├── unity-project.jpg     (Unity project screenshot - 400x250px)
│   ├── event-management.jpg  (Event photos - 400x250px)
│   └── programming-projects.jpg (Code/project screenshots - 400x250px)
├── index.html
├── style.css
└── script.js
```

## 🎯 **Step-by-Step Instructions**

### **Method 1: Add Local Images (Recommended)**

1. **Prepare Your Images:**
   - **Logo**: 40x40px, PNG format with transparent background
   - **Profile Picture**: 300x300px, square format, JPG/PNG
   - **About Photo**: 400x500px, portrait orientation
   - **Project Images**: 400x250px, landscape format

2. **Save Images:**
   - Copy your images to the `images/` folder
   - Use these exact names or update the HTML accordingly:
     - `logo.png` - Your personal logo
     - `profile-pic.jpg` - Your main photo
     - `about-me.jpg` - Professional photo for about section
     - `unity-project.jpg` - Screenshot of your Unity work
     - `event-management.jpg` - Photo from events you managed
     - `programming-projects.jpg` - Code or project screenshot

### **Method 2: Use Online Images**

If you prefer using images from the web:

1. **Find Your Images Online**
2. **Right-click → Copy Image Address**
3. **Replace the src attributes in HTML:**

```html
<!-- Example: Replace local path with online URL -->
<img src="https://your-image-url.com/photo.jpg" alt="Your Name" />
```

### **Method 3: Use Image Hosting Services**

**Free Options:**
- **Imgur**: Upload → Copy direct link
- **Google Photos**: Share → Copy link
- **GitHub**: Upload to repository → Use raw URL

**Professional Options:**
- **Cloudinary**: Advanced image optimization
- **AWS S3**: Enterprise-level hosting

## 🛠️ **Image Optimization Tips**

### **Recommended Sizes:**
- **Logo**: 40x40px to 80x80px
- **Profile Picture**: 300x300px to 500x500px
- **About Photo**: 400x500px to 600x750px
- **Project Images**: 400x250px to 800x500px

### **File Formats:**
- **Photos**: JPG (smaller file size)
- **Graphics/Logo**: PNG (supports transparency)
- **Modern browsers**: WebP (best compression)

### **Compression:**
- Use tools like TinyPNG or Squoosh.app
- Keep file sizes under 500KB for fast loading

## 🎨 **Customization Options**

### **Change Image Styles in CSS:**

```css
/* Make profile picture more creative */
.profile-pic {
  border-radius: 20px; /* Rounded corners instead of circle */
  filter: grayscale(50%); /* Artistic effect */
  transition: all 0.3s ease;
}

.profile-pic:hover {
  filter: grayscale(0%); /* Color on hover */
  transform: scale(1.05);
}

/* Customize logo */
.logo-img {
  width: 50px; /* Adjust size */
  height: 50px;
  border-radius: 10px; /* Square with rounded corners */
}
```

### **Add Background Images:**

```css
/* Hero section background */
.hero {
  background-image: url('images/hero-background.jpg');
  background-size: cover;
  background-position: center;
}
```

## 📱 **Mobile Optimization**

Images automatically resize for mobile devices, but you can add specific mobile styles:

```css
@media (max-width: 768px) {
  .profile-pic {
    width: 200px;
    height: 200px;
  }
  
  .logo-img {
    width: 30px;
    height: 30px;
  }
}
```

## 🔧 **Troubleshooting**

### **Image Not Showing?**
1. Check file path spelling
2. Ensure image exists in `images/` folder
3. Check file extension (jpg vs jpeg)
4. Verify image isn't corrupted

### **Image Too Large/Small?**
1. Edit CSS width/height properties
2. Use image editing software to resize
3. Check responsive design on mobile

### **Slow Loading?**
1. Compress images using online tools
2. Use appropriate file formats
3. Consider lazy loading for large images

## 🚀 **Quick Start**

1. **Create/Find Your Images**
2. **Resize them to recommended dimensions**
3. **Save them in the `images/` folder with correct names**
4. **Refresh your website**

Your images will automatically appear with all the creative animations and effects intact!

## 💡 **Pro Tips**

- Use consistent image styles (same filter, border-radius)
- Optimize for web to improve loading speed
- Consider using a favicon (16x16px icon for browser tab)
- Add alt text for accessibility
- Test on different devices and screen sizes

Your website will look amazing with your personal photos! 🎉
