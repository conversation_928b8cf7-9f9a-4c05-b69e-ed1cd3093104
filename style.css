/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* SMOOTH TECH SPLASH SCREEN */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background:
    radial-gradient(circle at 30% 40%, rgba(0, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 70% 60%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.8s ease;
}

/* Subtle Tech Grid */
.splash-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.02) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: subtleGridMove 30s linear infinite;
  z-index: 1;
}

/* SMOOTH LIQUID SYSTEM */
.water-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0%;
  overflow: hidden;
  z-index: 4;
}

.water-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(180deg,
      rgba(0, 255, 255, 0.8) 0%,
      rgba(64, 224, 208, 0.85) 30%,
      rgba(138, 43, 226, 0.9) 70%,
      rgba(75, 0, 130, 0.95) 100%);
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  box-shadow:
    0 -20px 40px rgba(0, 255, 255, 0.4),
    0 -40px 80px rgba(138, 43, 226, 0.3);
}

.water-fill.active {
  opacity: 1;
  transform: translateY(0);
  animation: smoothFlood 3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.water-surface {
  position: absolute;
  top: -10px;
  left: 0;
  width: 100%;
  height: 20px;
  background:
    linear-gradient(90deg,
      transparent 0%,
      rgba(0, 255, 255, 0.6) 50%,
      transparent 100%);
  opacity: 0;
  animation: smoothWaves 2s ease-in-out infinite;
}

.water-fill.active + .water-surface {
  opacity: 1;
}

/* WATER SPLASH EFFECTS */
.water-splash {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.splash-drop {
  position: absolute;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, rgba(173, 216, 230, 0.9) 0%, rgba(0, 191, 255, 0.6) 100%);
  border-radius: 50%;
  animation: splashDrop linear;
}

/* SMOOTH BUBBLE SYSTEM */
.bubbles-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
  overflow: hidden;
}

.bubble {
  position: absolute;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(0, 255, 255, 0.6) 40%,
      rgba(138, 43, 226, 0.4) 100%);
  box-shadow:
    0 4px 8px rgba(0, 255, 255, 0.3),
    inset -2px -2px 4px rgba(255, 255, 255, 0.5);
  animation: smoothBubbleRise linear;
  opacity: 0.7;
}

.bubble::before {
  content: '';
  position: absolute;
  top: 20%;
  left: 25%;
  width: 25%;
  height: 25%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, transparent 70%);
  border-radius: 50%;
}

/* LARGE BUBBLE VARIANTS */
.bubble.large {
  filter: blur(0.5px);
  animation-duration: 6s !important;
}

.bubble.medium {
  filter: blur(0.2px);
  animation-duration: 4s !important;
}

.bubble.small {
  animation-duration: 3s !important;
}

/* DRAMATIC SCREEN BREAK EFFECT */
.screen-break {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 6;
  opacity: 0;
}

.screen-break.active {
  opacity: 1;
  animation: screenShatter 2s ease-out forwards;
}

.crack {
  position: absolute;
  background:
    linear-gradient(var(--crack-angle, 45deg),
      transparent 0%,
      rgba(0, 0, 0, 0.9) 20%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(0, 0, 0, 0.9) 80%,
      transparent 100%);
  transform-origin: center;
  opacity: 0;
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.8));
}

.crack.animate {
  animation: dramaticCrackGrow 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.crack-1 {
  top: 25%;
  left: 15%;
  width: 300px;
  height: 4px;
  --crack-angle: 45deg;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.crack-2 {
  top: 65%;
  right: 20%;
  width: 250px;
  height: 3px;
  --crack-angle: -30deg;
  transform: rotate(-30deg);
  animation-delay: 0.15s;
}

.crack-3 {
  top: 15%;
  right: 10%;
  width: 280px;
  height: 4px;
  --crack-angle: 60deg;
  transform: rotate(60deg);
  animation-delay: 0.3s;
}

.crack-4 {
  bottom: 35%;
  left: 25%;
  width: 200px;
  height: 3px;
  --crack-angle: -45deg;
  transform: rotate(-45deg);
  animation-delay: 0.45s;
}

.crack-5 {
  top: 45%;
  left: 40%;
  width: 350px;
  height: 5px;
  --crack-angle: 15deg;
  transform: rotate(15deg);
  animation-delay: 0.6s;
}

.crack-6 {
  top: 35%;
  left: 60%;
  width: 180px;
  height: 3px;
  --crack-angle: -60deg;
  transform: rotate(-60deg);
  animation-delay: 0.75s;
}

.crack-7 {
  bottom: 20%;
  right: 30%;
  width: 220px;
  height: 4px;
  --crack-angle: 30deg;
  transform: rotate(30deg);
  animation-delay: 0.9s;
}

/* SMOOTH FLOATING CONTENT */
.splash-content {
  text-align: center;
  position: relative;
  z-index: 2;
  animation: gentleFloat 4s ease-in-out infinite;
  transition: all 2s ease;
}

.splash-content.floating {
  animation: smoothSweptAway 2.5s ease-out forwards;
}

.logo-container {
  margin-bottom: 2rem;
  transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

/* Smooth Tech Logo */
.splash-logo {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(0, 255, 255, 0.6);
  box-shadow:
    0 15px 30px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 255, 255, 0.4);
  transition: all 0.3s ease;
  animation: gentleLogoFloat 5s ease-in-out infinite;
}

.splash-screen:hover .splash-logo {
  transform: scale(1.05);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(0, 255, 255, 0.6);
  border-color: rgba(0, 255, 255, 0.8);
}

/* Smooth Tech Name */
.splash-name {
  font-family: 'Poppins', sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1rem;
  text-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 255, 255, 0.5);
  transition: all 2s ease;
  animation: gentleNameGlow 4s ease-in-out infinite alternate;
}

/* Smooth Subtitle */
.splash-subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  color: rgba(0, 255, 255, 0.8);
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: gentlePulse 3s ease-in-out infinite;
  transition: all 2s ease;
}







.splash-screen:hover .splash-logo {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.logo-ripple, .logo-ripple-2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: ripple 2s infinite;
}

.logo-ripple {
  width: 140px;
  height: 140px;
}

.logo-ripple-2 {
  width: 160px;
  height: 160px;
  animation-delay: 1s;
}

.splash-name {
  font-family: 'Poppins', sans-serif;
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: nameGlow 2s ease-in-out infinite alternate;
}

.splash-subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  animation: pulse 2s ease-in-out infinite;
}

.splash-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  opacity: 0.7;
  animation: orbFloat 8s ease-in-out infinite;
}

.orb-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(255, 234, 167, 0.4) 0%, transparent 70%);
  bottom: 20%;
  right: 15%;
  animation-delay: 2s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(240, 147, 251, 0.3) 0%, transparent 70%);
  top: 50%;
  right: 10%;
  animation-delay: 4s;
}

.splash-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: particleFloat 6s linear infinite;
}

.particle:nth-child(1) {
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  left: 40%;
  animation-delay: 1s;
}

.particle:nth-child(3) {
  left: 60%;
  animation-delay: 2s;
}

.particle:nth-child(4) {
  left: 80%;
  animation-delay: 3s;
}

.particle:nth-child(5) {
  left: 30%;
  animation-delay: 4s;
}

.particle:nth-child(6) {
  left: 70%;
  animation-delay: 5s;
}

/* Main Content Hidden Initially */
.main-content {
  opacity: 1;
  transition: all 1s ease;
}

.main-content.hidden {
  opacity: 0;
  transform: translateY(50px);
}

.main-content.show {
  opacity: 1;
  transform: translateY(0);
  animation: contentSlideIn 1s ease-out;
}

/* SMOOTH ANIMATIONS */

/* Subtle Grid Movement */
@keyframes subtleGridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

/* Smooth Flood Animation */
@keyframes smoothFlood {
  0% {
    height: 0%;
  }
  100% {
    height: 100%;
  }
}

/* Smooth Waves */
@keyframes smoothWaves {
  0%, 100% {
    transform: translateX(-100%);
    opacity: 0.6;
  }
  50% {
    transform: translateX(100%);
    opacity: 1;
  }
}

/* Smooth Bubble Rise */
@keyframes smoothBubbleRise {
  0% {
    transform: translateY(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
    transform: scale(1);
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100vh) scale(0.8);
    opacity: 0;
  }
}

/* Gentle Content Float */
@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

/* Gentle Logo Float */
@keyframes gentleLogoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-5px) rotate(2deg);
  }
}

/* Gentle Name Glow */
@keyframes gentleNameGlow {
  from {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(0, 255, 255, 0.5);
  }
  to {
    text-shadow:
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(0, 255, 255, 0.7);
  }
}

/* Gentle Pulse */
@keyframes gentlePulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Smooth Swept Away */
@keyframes smoothSweptAway {
  0% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-30px) translateX(100px) scale(0.8);
    opacity: 0.6;
  }
  100% {
    transform: translateY(-80px) translateX(250px) scale(0.3);
    opacity: 0;
  }
}

/* Parallax Float Animation */
@keyframes parallaxFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 1;
  }
}

/* Scroll Reveal Animations */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Header Animation Keyframes */
@keyframes logoRingRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Tech Elements Animations */
@keyframes techFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-15px) rotate(5deg);
    opacity: 1;
  }
}

@keyframes techPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes techGlow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 30px rgba(0, 255, 255, 1), 0 0 40px rgba(255, 20, 147, 0.5);
  }
}

/* Interactive Robot Animations */
@keyframes robotBreathe {
  0%, 100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
}

@keyframes robotExcited {
  0%, 100% {
    transform: scale(1.2) rotate(-2deg);
  }
  50% {
    transform: scale(1.3) rotate(2deg);
  }
}

@keyframes trailPulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0.1;
  }
}

@keyframes attentionBlink {
  0%, 70%, 100% {
    opacity: 0;
    transform: scale(1);
  }
  75%, 95% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes attentionRipple {
  0%, 70%, 100% {
    transform: scale(1);
    opacity: 0;
  }
  75% {
    transform: scale(1);
    opacity: 0.5;
  }
  95% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Footprint Animations */
@keyframes footprintFade {
  0% {
    opacity: 0.7;
    transform: scale(0.9);
  }
  20% {
    opacity: 0.7;
    transform: scale(1);
  }
  80% {
    opacity: 0.4;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}



/* Splash Drop Effect */
@keyframes splashDrop {
  0% {
    transform: translateY(-20px) scale(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    transform: translateY(100vh) scale(0.5);
    opacity: 0;
  }
}





/* Dramatic Crack Growth */
@keyframes dramaticCrackGrow {
  0% {
    opacity: 0;
    transform: scale(0, 1) rotate(var(--crack-angle, 0deg));
    filter: drop-shadow(0 0 0px rgba(0, 0, 0, 0));
  }
  20% {
    opacity: 0.3;
    transform: scale(0.2, 1) rotate(var(--crack-angle, 0deg));
  }
  50% {
    opacity: 0.8;
    transform: scale(0.6, 1) rotate(var(--crack-angle, 0deg));
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
  }
  80% {
    opacity: 1;
    transform: scale(0.9, 1) rotate(var(--crack-angle, 0deg));
    filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.8));
  }
  100% {
    opacity: 1;
    transform: scale(1, 1) rotate(var(--crack-angle, 0deg));
    filter: drop-shadow(0 0 15px rgba(0, 0, 0, 1));
  }
}

/* Screen Shatter Effect */
@keyframes screenShatter {
  0% {
    filter: blur(0px);
  }
  50% {
    filter: blur(2px);
  }
  100% {
    filter: blur(5px);
    transform: scale(1.05);
  }
}

/* Simple Pulse Animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* NAME GLITCH EFFECTS */
@keyframes nameGlitch {
  0%, 100% {
    transform: translateX(0);
  }
  10% {
    transform: translateX(-2px);
  }
  20% {
    transform: translateX(2px);
  }
  30% {
    transform: translateX(-1px);
  }
  40% {
    transform: translateX(1px);
  }
  50% {
    transform: translateX(-2px);
  }
  60% {
    transform: translateX(2px);
  }
  70% {
    transform: translateX(-1px);
  }
  80% {
    transform: translateX(1px);
  }
  90% {
    transform: translateX(-2px);
  }
}

@keyframes glitchLayer1 {
  0%, 100% {
    transform: translateX(0);
    opacity: 1;
  }
  10% {
    transform: translateX(-5px);
    opacity: 0.8;
  }
  20% {
    transform: translateX(5px);
    opacity: 0.6;
  }
  30% {
    transform: translateX(-3px);
    opacity: 0.9;
  }
  40% {
    transform: translateX(3px);
    opacity: 0.7;
  }
  50% {
    transform: translateX(-2px);
    opacity: 0.8;
  }
}

@keyframes glitchLayer2 {
  0%, 100% {
    transform: translateX(0);
    opacity: 0.8;
  }
  15% {
    transform: translateX(3px);
    opacity: 0.6;
  }
  25% {
    transform: translateX(-3px);
    opacity: 0.9;
  }
  35% {
    transform: translateX(2px);
    opacity: 0.7;
  }
  45% {
    transform: translateX(-2px);
    opacity: 0.8;
  }
  55% {
    transform: translateX(4px);
    opacity: 0.6;
  }
}

@keyframes glitchLayer3 {
  0%, 100% {
    transform: translateX(0);
    opacity: 0.9;
  }
  5% {
    transform: translateX(2px);
    opacity: 0.7;
  }
  15% {
    transform: translateX(-2px);
    opacity: 0.8;
  }
  25% {
    transform: translateX(4px);
    opacity: 0.6;
  }
  35% {
    transform: translateX(-4px);
    opacity: 0.9;
  }
  45% {
    transform: translateX(1px);
    opacity: 0.8;
  }
}

/* TYPEWRITER AND CURSOR */
@keyframes typewriterBlink {
  0%, 50% {
    border-color: #00ff41;
  }
  51%, 100% {
    border-color: transparent;
  }
}

@keyframes cursorBlink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* INTERACTION HINTS */
@keyframes hintFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulseBeat {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes pulseRipple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes arrowGlow {
  from {
    box-shadow: 0 0 10px #00ff41;
  }
  to {
    box-shadow: 0 0 20px #00ff41, 0 0 30px #00ff41;
  }
}

/* PARTICLE ORBIT */
@keyframes particleOrbit {
  0% {
    transform: rotate(0deg) translateX(50px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(50px) rotate(-360deg);
  }
}

/* SCAN LINE MOVEMENT */
@keyframes scanLineMove {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes nameGlow {
  from {
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
  to {
    text-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes orbFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: #e0e6ed;
  background: #0a0e27;
  overflow-x: hidden;
}

/* Tech Background Pattern */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 20, 147, 0.02) 0%, transparent 50%);
  z-index: -1;
  pointer-events: none;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Enhanced Tech Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(10, 14, 39, 0.95);
  backdrop-filter: blur(15px);
  z-index: 1000;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 255, 255, 0.1);
  padding: 1rem 0;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
  gap: 2rem;
}

/* Enhanced Logo Section */
.nav-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-container-nav {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(0, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.logo-glow-ring {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 50%;
  animation: logoRingRotate 4s linear infinite;
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.logo-text {
  font-size: 1.4rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 0.8rem;
  color: rgba(0, 255, 255, 0.7);
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Status Indicators */
.status-indicators {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-dot.available {
  background: #00ff41;
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.6);
}

.status-text {
  font-size: 0.85rem;
  color: #e0e6ed;
  font-weight: 500;
}

.tech-stats {
  display: flex;
  gap: 1.2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.2rem;
  min-width: 60px;
}

.stat-number {
  font-size: 1.1rem;
  font-weight: 700;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  white-space: nowrap;
}

.stat-label {
  font-size: 0.65rem;
  color: rgba(224, 230, 237, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
  white-space: nowrap;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-hire {
  padding: 0.6rem 1.2rem;
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  color: #0a0e27;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.btn-hire:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 255, 255, 0.5);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background: rgba(0, 255, 255, 0.2);
  transform: rotate(180deg);
}

.toggle-icon {
  font-size: 1.2rem;
  color: #00ffff;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #e0e6ed;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.nav-link:hover::after {
  width: 100%;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: #333;
  margin: 3px 0;
  transition: 0.3s;
}

/* Tech Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.15) 0%, transparent 50%),
    linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-text {
  color: #e0e6ed;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  opacity: 0;
  animation: slideInLeft 1s ease 0.5s forwards;
}

.highlight {
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  height: 2rem;
  opacity: 0;
  animation: slideInLeft 1s ease 0.7s forwards;
}

.typing-text {
  border-right: 2px solid #00ffff;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: #00ffff; }
}

.hero-description {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  opacity: 0;
  animation: slideInLeft 1s ease 0.9s forwards;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  opacity: 0;
  animation: slideInLeft 1s ease 1.1s forwards;
}

.btn {
  padding: 12px 30px;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  color: #0a0e27;
  border: none;
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.5);
}

.btn-secondary {
  background: transparent;
  color: #00ffff;
  border: 2px solid #00ffff;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: #00ffff;
  color: #0a0e27;
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(0, 255, 255, 0.6);
}

.hero-image {
  display: flex;
  justify-content: center;
  opacity: 0;
  animation: slideInRight 1s ease 0.5s forwards;
}

.profile-container {
  position: relative;
}

.profile-pic {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.profile-pic:hover {
  transform: scale(1.05);
}

.profile-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  width: 320px;
  height: 320px;
  border: 2px solid rgba(255, 234, 167, 0.5);
  border-radius: 50%;
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.scroll-arrow {
  color: white;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
  cursor: pointer;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Animation Keyframes */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tech Section Styles with Scroll Animations */
section {
  padding: 100px 0;
}

/* Scroll Animation Sections (exclude hero) */
section:not(.hero) {
  background: rgba(10, 14, 39, 0.3);
  margin: 2rem 0;
  border-radius: 20px;
  border: 1px solid rgba(0, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

section:not(.hero).animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Parallax Background Elements */
.parallax-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.parallax-element {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(0, 255, 255, 0.6);
  border-radius: 50%;
  animation: parallaxFloat 8s ease-in-out infinite;
}

.parallax-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.parallax-element:nth-child(2) { top: 20%; left: 80%; animation-delay: 1s; }
.parallax-element:nth-child(3) { top: 60%; left: 20%; animation-delay: 2s; }
.parallax-element:nth-child(4) { top: 80%; left: 70%; animation-delay: 3s; }
.parallax-element:nth-child(5) { top: 40%; left: 90%; animation-delay: 4s; }

/* Interactive Scroll Robot */
.interactive-robot {
  position: fixed;
  right: 50px;
  top: 10%;
  z-index: 1000;
  pointer-events: none;
  transition: top 1.5s ease-out, right 0.3s ease-out;
  opacity: 1;
  visibility: visible;
}

.robot-body {
  position: relative;
  font-size: 3.5rem;
  color: #00ffff;
  text-shadow:
    0 0 20px rgba(0, 255, 255, 0.8),
    0 0 40px rgba(0, 255, 255, 0.6);
  animation: robotBreathe 2s ease-in-out infinite;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
}

.robot-body:hover {
  transform: scale(1.2);
  color: #ff1493;
  text-shadow:
    0 0 25px rgba(255, 20, 147, 0.9),
    0 0 50px rgba(255, 20, 147, 0.7);
  animation: robotExcited 0.5s ease-in-out infinite;
}

/* Robot Speech Bubble */
.robot-speech-bubble {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(10, 14, 39, 0.95);
  border: 2px solid rgba(0, 255, 255, 0.6);
  border-radius: 20px;
  padding: 10px 15px;
  min-width: 120px;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.robot-speech-bubble::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(0, 255, 255, 0.6);
}

.robot-speech-bubble.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-10px);
}

.speech-text {
  color: #e0e6ed;
  font-size: 0.9rem;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Robot Footprints */
.robot-footprints-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 999;
}

.robot-footprint {
  position: fixed;
  width: 12px;
  height: 14px;
  animation: footprintFade 3s ease-out forwards;
  opacity: 0.7;
}

/* Paw Print Shape */
.robot-footprint::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 7px;
  background: rgba(0, 255, 255, 0.6);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.4);
}

/* Paw Pads */
.robot-footprint::after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 5px;
  background: rgba(0, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.4);
}

/* Additional toe pads */
.robot-footprint {
  background:
    radial-gradient(1.5px 1.5px at 2px 6px, rgba(0, 255, 255, 0.5) 50%, transparent 50%),
    radial-gradient(1.5px 1.5px at 10px 6px, rgba(0, 255, 255, 0.5) 50%, transparent 50%),
    radial-gradient(1.5px 1.5px at 4px 4px, rgba(0, 255, 255, 0.5) 50%, transparent 50%),
    radial-gradient(1.5px 1.5px at 8px 4px, rgba(0, 255, 255, 0.5) 50%, transparent 50%);
}

/* Attention Pointer */
.robot-attention-pointer {
  position: absolute;
  top: -20px;
  left: -20px;
  width: 8px;
  height: 8px;
  background: #ff1493;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(255, 20, 147, 0.8);
  opacity: 0;
  animation: attentionBlink 3s ease-in-out infinite;
}

.robot-attention-pointer::before {
  content: '';
  position: absolute;
  top: -15px;
  left: -15px;
  width: 38px;
  height: 38px;
  border: 2px solid rgba(255, 20, 147, 0.5);
  border-radius: 50%;
  animation: attentionRipple 3s ease-in-out infinite;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease 0.2s;
}

.section-header.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #00ffff 0%, #ff1493 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s ease 0.4s;
}

.section-title.animate-in {
  opacity: 1;
  transform: scale(1);
}

.section-subtitle {
  font-size: 1.1rem;
  color: #a0a6ad;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease 0.6s;
}

.section-subtitle.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* About Section */
.about {
  background: rgba(10, 14, 39, 0.3) !important;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text, .about-image {
  opacity: 0;
  transition: all 0.8s ease;
}

.about-text {
  transform: translateX(-50px);
}

.about-image {
  transform: translateX(50px);
}

.about-text.animate-in, .about-image.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.about-text h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.about-text p {
  margin-bottom: 1.5rem;
  color: #666;
  line-height: 1.8;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.stat {
  text-align: center;
  padding: 1.5rem;
  background: rgba(10, 14, 39, 0.8);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 255, 255, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 255, 255, 0.2);
  position: relative;
  overflow: visible;
}

.stat:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
  border-color: rgba(0, 255, 255, 0.4);
}

.stat h4 {
  font-size: 2rem;
  font-weight: 700;
  color: #00ffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.stat p {
  color: #e0e6ed;
  font-weight: 500;
}

/* Interactive Stat Styling */
.interactive-stat {
  cursor: pointer;
  position: relative;
}

.interactive-stat:hover {
  background: rgba(10, 14, 39, 0.9);
}

/* Popup Styling */
.stat-popup {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%) scale(0.8);
  background: rgba(10, 14, 39, 0.98);
  border: 2px solid rgba(0, 255, 255, 0.6);
  border-radius: 15px;
  padding: 1.5rem;
  min-width: 280px;
  max-width: 320px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 30px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1000;
}

.stat-popup::before {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(0, 255, 255, 0.6);
}

.interactive-stat:hover .stat-popup {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-100%) scale(1);
}

.popup-header {
  font-size: 1.1rem;
  font-weight: 700;
  color: #00ffff;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  padding-bottom: 0.5rem;
}

.popup-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.popup-item {
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 8px;
  color: #e0e6ed;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 255, 255, 0.2);
  text-align: center;
  opacity: 0;
  transform: translateY(10px);
}

.popup-item:hover {
  background: rgba(0, 255, 255, 0.2);
  color: #ffffff;
  border-color: rgba(0, 255, 255, 0.4);
  transform: translateY(10px) translateX(5px);
}

.interactive-stat:hover .popup-item {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered Animation for Popup Items */
.interactive-stat:hover .popup-item:nth-child(1) { transition-delay: 0.1s; }
.interactive-stat:hover .popup-item:nth-child(2) { transition-delay: 0.2s; }
.interactive-stat:hover .popup-item:nth-child(3) { transition-delay: 0.3s; }
.interactive-stat:hover .popup-item:nth-child(4) { transition-delay: 0.4s; }
.interactive-stat:hover .popup-item:nth-child(5) { transition-delay: 0.5s; }
.interactive-stat:hover .popup-item:nth-child(6) { transition-delay: 0.6s; }
.interactive-stat:hover .popup-item:nth-child(7) { transition-delay: 0.7s; }
.interactive-stat:hover .popup-item:nth-child(8) { transition-delay: 0.8s; }

.about-img-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}

.about-img-container img {
  width: 100%;
  height: auto;
  transition: all 0.3s ease;
}

.about-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.about-img-container:hover .about-overlay {
  opacity: 1;
}

.about-img-container:hover img {
  transform: scale(1.1);
}

/* Skills Section with Scroll Animations */
.skills {
  background: rgba(10, 14, 39, 0.2) !important;
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
}

.skill-category {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.skill-category.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.skill-category:nth-child(1) { transition-delay: 0.1s; }
.skill-category:nth-child(2) { transition-delay: 0.3s; }
.skill-category:nth-child(3) { transition-delay: 0.5s; }

.skill-category {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.skill-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.category-icon i {
  font-size: 1.5rem;
  color: white;
}

.skill-category h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.skill-bar {
  margin-bottom: 1.5rem;
}

.skill-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.skill-info span {
  font-weight: 500;
  color: #333;
}

.skill-progress {
  height: 8px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.skill-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  width: 0;
  transition: width 1.5s ease;
}

/* Projects Section with Scroll Animations */
.projects {
  background: rgba(10, 14, 39, 0.2) !important;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.project-card {
  opacity: 0;
  transform: translateY(40px) scale(0.9);
  transition: all 0.7s ease;
}

.project-card.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.project-card:nth-child(1) { transition-delay: 0.1s; }
.project-card:nth-child(2) { transition-delay: 0.3s; }
.project-card:nth-child(3) { transition-delay: 0.5s; }

.project-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-links {
  display: flex;
  gap: 1rem;
}

.project-link {
  width: 50px;
  height: 50px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.project-link:hover {
  background: #667eea;
  color: white;
  transform: scale(1.1);
}

.project-info {
  padding: 1.5rem;
}

.project-info h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.project-info p {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tech-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Contact Section with Scroll Animations */
.contact {
  background: rgba(10, 14, 39, 0.2) !important;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info, .contact-form {
  opacity: 0;
  transition: all 0.8s ease;
}

.contact-info {
  transform: translateX(-40px);
}

.contact-form {
  transform: translateX(40px);
}

.contact-info.animate-in, .contact-form.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  transform: translateX(10px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.contact-details h4 {
  margin-bottom: 0.5rem;
  color: #333;
}

.contact-details a {
  color: #667eea;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-details a:hover {
  color: #764ba2;
}

.form {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 20px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: #999;
}

/* Footer */
.footer {
  background: #333;
  color: white;
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #667eea;
  transform: translateY(-3px);
}

/* Scroll Animations */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Splash Screen Responsive */
@media (max-width: 768px) {
  .splash-name {
    font-size: 2.5rem;
  }

  .splash-logo {
    width: 80px;
    height: 80px;
  }

  .logo-ripple {
    width: 100px;
    height: 100px;
  }

  .logo-ripple-2 {
    width: 120px;
    height: 120px;
  }

  .splash-subtitle {
    font-size: 1rem;
  }

  .gradient-orb {
    width: 150px !important;
    height: 150px !important;
  }
}

@media (max-width: 480px) {
  .splash-name {
    font-size: 2rem;
  }

  .splash-logo {
    width: 60px;
    height: 60px;
  }

  .logo-ripple {
    width: 80px;
    height: 80px;
  }

  .logo-ripple-2 {
    width: 100px;
    height: 100px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    height: 70px;
    gap: 1rem;
  }

  .status-indicators {
    display: none;
  }

  .tech-stats {
    gap: 0.8rem;
  }

  .stat-item {
    min-width: 50px;
  }

  .stat-number {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.6rem;
  }

  .quick-actions {
    gap: 0.5rem;
  }

  .btn-hire {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .theme-toggle {
    width: 35px;
    height: 35px;
  }

  .brand-info {
    gap: 0.1rem;
  }

  .logo-text {
    font-size: 1.2rem;
  }

  .brand-tagline {
    font-size: 0.7rem;
  }

  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background: rgba(10, 14, 39, 0.98);
    backdrop-filter: blur(20px);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 255, 255, 0.1);
    border-top: 1px solid rgba(0, 255, 255, 0.2);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-stats {
    grid-template-columns: 1fr;
  }

  .stat-popup {
    min-width: 250px;
    max-width: 280px;
    padding: 1rem;
  }

  .popup-header {
    font-size: 1rem;
  }

  .popup-item {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  /* Interactive Robot Responsive */
  .interactive-robot {
    left: 5%;
  }

  .robot-body {
    font-size: 3rem;
  }

  .robot-speech-bubble {
    min-width: 100px;
    padding: 8px 12px;
    top: -70px;
  }

  .speech-text {
    font-size: 0.8rem;
  }

  .robot-footprint {
    width: 15px;
    height: 15px;
  }

  .skills-categories {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .status-indicators {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .status-item {
    display: none;
  }

  .tech-stats {
    gap: 0.5rem;
  }

  .stat-item {
    min-width: 40px;
  }

  .stat-number {
    font-size: 0.9rem;
  }

  .stat-label {
    font-size: 0.55rem;
  }

  .quick-actions .theme-toggle {
    display: none;
  }

  .logo-img {
    width: 40px;
    height: 40px;
  }

  .logo-text {
    font-size: 1rem;
  }

  .brand-tagline {
    display: none;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .btn {
    width: 100%;
    text-align: center;
  }

  /* Interactive Robot Mobile */
  .interactive-robot {
    left: 3%;
  }

  .robot-body {
    font-size: 2.5rem;
  }

  .robot-speech-bubble {
    min-width: 80px;
    padding: 6px 10px;
    top: -60px;
  }

  .speech-text {
    font-size: 0.7rem;
  }

  .robot-footprint {
    width: 12px;
    height: 12px;
  }

  .robot-attention-pointer {
    display: none; /* Hide attention pointer on mobile for performance */
  }
}
