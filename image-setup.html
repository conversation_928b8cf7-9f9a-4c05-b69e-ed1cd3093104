<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Setup Guide - <PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1, h2 {
            text-align: center;
            margin-bottom: 30px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .image-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .image-placeholder {
            width: 100%;
            height: 200px;
            background: rgba(255, 255, 255, 0.3);
            border: 2px dashed #fff;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .logo-placeholder {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
        }
        .profile-placeholder {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 0 auto 15px;
        }
        .steps {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .file-structure {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: monospace;
            margin: 20px 0;
        }
        .highlight {
            background: rgba(255, 234, 167, 0.3);
            padding: 2px 5px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image Setup Guide for Your Website</h1>
        
        <div class="steps">
            <h2>📋 Quick Setup Steps</h2>
            <div class="step">
                <strong>Step 1:</strong> Create an <span class="highlight">images</span> folder in your website directory
            </div>
            <div class="step">
                <strong>Step 2:</strong> Add your photos with the exact names shown below
            </div>
            <div class="step">
                <strong>Step 3:</strong> Refresh your website to see the changes
            </div>
        </div>

        <div class="file-structure">
            <strong>📁 Required File Structure:</strong><br><br>
            website/<br>
            ├── images/<br>
            │   ├── <span class="highlight">logo.png</span> (Your logo - 80x80px)<br>
            │   ├── <span class="highlight">profile-pic.jpg</span> (Main photo - 300x300px)<br>
            │   ├── <span class="highlight">about-me.jpg</span> (About section - 400x500px)<br>
            │   ├── <span class="highlight">unity-project.jpg</span> (Unity work - 400x250px)<br>
            │   ├── <span class="highlight">event-management.jpg</span> (Events - 400x250px)<br>
            │   └── <span class="highlight">programming-projects.jpg</span> (Code - 400x250px)<br>
            ├── index.html<br>
            ├── style.css<br>
            └── script.js
        </div>

        <h2>🎯 Images You Need to Add</h2>
        
        <div class="image-grid">
            <div class="image-card">
                <div class="image-placeholder logo-placeholder">
                    LOGO
                </div>
                <h3>logo.png</h3>
                <p><strong>Size:</strong> 80x80 pixels</p>
                <p><strong>Format:</strong> PNG (transparent background)</p>
                <p><strong>Use:</strong> Navigation bar logo</p>
            </div>

            <div class="image-card">
                <div class="image-placeholder profile-placeholder">
                    YOUR PHOTO
                </div>
                <h3>profile-pic.jpg</h3>
                <p><strong>Size:</strong> 300x300 pixels</p>
                <p><strong>Format:</strong> JPG or PNG</p>
                <p><strong>Use:</strong> Hero section main photo</p>
            </div>

            <div class="image-card">
                <div class="image-placeholder">
                    ABOUT PHOTO
                </div>
                <h3>about-me.jpg</h3>
                <p><strong>Size:</strong> 400x500 pixels</p>
                <p><strong>Format:</strong> JPG</p>
                <p><strong>Use:</strong> About section professional photo</p>
            </div>

            <div class="image-card">
                <div class="image-placeholder">
                    UNITY PROJECT
                </div>
                <h3>unity-project.jpg</h3>
                <p><strong>Size:</strong> 400x250 pixels</p>
                <p><strong>Format:</strong> JPG</p>
                <p><strong>Use:</strong> Screenshot of your Unity game</p>
            </div>

            <div class="image-card">
                <div class="image-placeholder">
                    EVENT PHOTOS
                </div>
                <h3>event-management.jpg</h3>
                <p><strong>Size:</strong> 400x250 pixels</p>
                <p><strong>Format:</strong> JPG</p>
                <p><strong>Use:</strong> Photo from events you managed</p>
            </div>

            <div class="image-card">
                <div class="image-placeholder">
                    CODE/PROJECTS
                </div>
                <h3>programming-projects.jpg</h3>
                <p><strong>Size:</strong> 400x250 pixels</p>
                <p><strong>Format:</strong> JPG</p>
                <p><strong>Use:</strong> Screenshot of your code or projects</p>
            </div>
        </div>

        <div class="steps">
            <h2>💡 Pro Tips</h2>
            <div class="step">
                <strong>Image Quality:</strong> Use high-resolution images but compress them for web
            </div>
            <div class="step">
                <strong>Consistency:</strong> Use similar lighting and style for all photos
            </div>
            <div class="step">
                <strong>File Size:</strong> Keep each image under 500KB for fast loading
            </div>
            <div class="step">
                <strong>Backup:</strong> Keep original high-resolution versions of your photos
            </div>
        </div>

        <div class="steps">
            <h2>🔧 Alternative: Use Online Images</h2>
            <div class="step">
                If you don't have local images ready, you can use online images by replacing the <code>src</code> attributes in the HTML file with direct image URLs.
            </div>
        </div>

        <p style="text-align: center; margin-top: 40px; font-size: 18px;">
            <strong>Once you add your images, your website will look absolutely stunning! 🚀</strong>
        </p>
    </div>
</body>
</html>
