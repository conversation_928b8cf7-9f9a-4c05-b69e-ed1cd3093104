// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize splash screen first
    initSplashScreen();

    // Initialize all features (will be called after splash screen)
    setTimeout(() => {
        initNavigation();
        initTypingAnimation();
        initScrollAnimations();
        initSkillBars();
        initSmoothScrolling();
        initContactForm();
        initCoolScrollAnimations();
        initParallaxEffect();

    }, 100);
});

// EPIC WATER FLOOD ANIMATION SYSTEM
function initSplashScreen() {
    const splashScreen = document.getElementById('splash-screen');
    const mainContent = document.getElementById('main-content');
    const waterContainer = document.querySelector('.water-container');
    const waterFill = document.querySelector('.water-fill');
    const bubblesContainer = document.querySelector('.bubbles-container');
    const waterSplash = document.querySelector('.water-splash');
    const screenBreak = document.querySelector('.screen-break');
    const splashContent = document.querySelector('.splash-content');

    let animationStarted = false;

    // Create tech-enhanced bubbles with advanced effects
    function createTechBubble() {
        const bubble = document.createElement('div');

        // Random bubble type with tech enhancement chance
        const bubbleTypes = ['small', 'medium', 'large'];
        const randomType = bubbleTypes[Math.floor(Math.random() * bubbleTypes.length)];
        const isTechEnhanced = Math.random() > 0.6; // 40% chance for tech enhancement

        bubble.className = `bubble ${randomType}${isTechEnhanced ? ' tech-enhanced' : ''}`;

        // Size based on type
        let size;
        switch(randomType) {
            case 'small': size = Math.random() * 18 + 10; break;
            case 'medium': size = Math.random() * 30 + 25; break;
            case 'large': size = Math.random() * 45 + 40; break;
        }

        bubble.style.width = size + 'px';
        bubble.style.height = size + 'px';

        // Random horizontal position
        bubble.style.left = Math.random() * 100 + '%';
        bubble.style.bottom = '-60px';

        // Random animation duration with tech variation
        const baseDuration = Math.random() * 4 + 4;
        const duration = isTechEnhanced ? baseDuration * 1.2 : baseDuration;
        bubble.style.animationDuration = duration + 's';

        // Add tech-specific styling
        if (isTechEnhanced) {
            const techColors = [
                'rgba(0, 255, 255, 0.8)',
                'rgba(255, 20, 147, 0.8)',
                'rgba(138, 43, 226, 0.8)',
                'rgba(75, 0, 130, 0.8)'
            ];
            const randomColor = techColors[Math.floor(Math.random() * techColors.length)];
            bubble.style.boxShadow += `, 0 0 ${size/2}px ${randomColor}`;
        }

        bubblesContainer.appendChild(bubble);

        // Remove bubble after animation
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.parentNode.removeChild(bubble);
            }
        }, duration * 1000);
    }

    // Create tech-enhanced splash drops
    function createTechSplashDrop() {
        const drop = document.createElement('div');
        drop.className = 'splash-drop';

        // Random position
        drop.style.left = Math.random() * 100 + '%';
        drop.style.top = Math.random() * 60 + '%';

        // Random size for variety
        const size = Math.random() * 6 + 4;
        drop.style.width = size + 'px';
        drop.style.height = size + 'px';

        // Tech color variations
        const techColors = [
            'rgba(0, 255, 255, 0.9)',
            'rgba(255, 20, 147, 0.8)',
            'rgba(138, 43, 226, 0.7)',
            'rgba(75, 0, 130, 0.6)'
        ];
        const randomColor = techColors[Math.floor(Math.random() * techColors.length)];
        drop.style.background = `radial-gradient(circle, ${randomColor} 0%, transparent 70%)`;
        drop.style.boxShadow = `0 0 ${size * 2}px ${randomColor}`;

        // Random animation duration
        const duration = Math.random() * 2.5 + 1.5;
        drop.style.animationDuration = duration + 's';

        waterSplash.appendChild(drop);

        // Remove drop after animation
        setTimeout(() => {
            if (drop.parentNode) {
                drop.parentNode.removeChild(drop);
            }
        }, duration * 1000);
    }

    // Epic water flood animation sequence
    function startEpicFloodAnimation() {
        if (animationStarted) return;
        animationStarted = true;

        // Phase 1: Start gentle bubbles
        const bubbleInterval = setInterval(createTechBubble, 300);

        // Phase 2: Start water flood after 1 second
        setTimeout(() => {
            waterContainer.style.height = '100%';
            waterFill.classList.add('active');

            // Start gentle splash drops
            const splashInterval = setInterval(createTechSplashDrop, 400);

            // Stop splash drops after 3 seconds
            setTimeout(() => clearInterval(splashInterval), 3000);
        }, 1000);

        // Phase 3: Content gets swept away
        setTimeout(() => {
            splashContent.classList.add('floating');
        }, 2500);

        // Phase 4: Stop bubbles and start screen break
        setTimeout(() => {
            clearInterval(bubbleInterval);
            startDramaticScreenBreak();
        }, 4000);
    }

    // Dramatic screen breaking effect
    function startDramaticScreenBreak() {
        screenBreak.classList.add('active');

        // Animate each crack with dramatic timing
        const cracks = document.querySelectorAll('.crack');
        cracks.forEach((crack, index) => {
            setTimeout(() => {
                crack.classList.add('animate');

                // Add screen shake effect
                if (index === 2) {
                    splashScreen.style.animation = 'screenShake 0.5s ease-in-out';
                }
            }, index * 150);
        });

        // Final dramatic transition
        setTimeout(() => {
            splashScreen.style.transform = 'scale(1.1)';
            splashScreen.style.filter = 'blur(3px)';
            splashScreen.style.opacity = '0';

            setTimeout(() => {
                mainContent.classList.remove('hidden');
                mainContent.classList.add('show');
                splashScreen.remove();
            }, 800);
        }, 2000);
    }

    // Click handler
    splashScreen.addEventListener('click', startEpicFloodAnimation);

    // Enhanced keyboard support
    document.addEventListener('keydown', function(e) {
        if ((e.key === 'Enter' || e.key === ' ' || e.key === 'Escape') && !animationStarted) {
            e.preventDefault();
            startEpicFloodAnimation();
        }
    });

    // Auto-start after 6 seconds (more time to appreciate the initial state)
    setTimeout(() => {
        if (!animationStarted) {
            startEpicFloodAnimation();
        }
    }, 6000);

    // Add screen shake animation to CSS dynamically
    const style = document.createElement('style');
    style.textContent = `
        @keyframes screenShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
}

// Navigation functionality
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Toggle mobile menu
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Change navbar background on scroll (Tech Theme)
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(10, 14, 39, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 255, 255, 0.2)';
        } else {
            navbar.style.background = 'rgba(10, 14, 39, 0.95)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 255, 255, 0.1)';
        }
    });
}

// Typing animation for hero section
function initTypingAnimation() {
    const typingText = document.querySelector('.typing-text');
    const texts = [
        'B.Tech CSE Student',
        'Python Developer',
        'Game Developer',
        'Event Manager',
        'Tech Enthusiast'
    ];
    
    let textIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let typingSpeed = 100;

    function typeText() {
        const currentText = texts[textIndex];
        
        if (isDeleting) {
            typingText.textContent = currentText.substring(0, charIndex - 1);
            charIndex--;
            typingSpeed = 50;
        } else {
            typingText.textContent = currentText.substring(0, charIndex + 1);
            charIndex++;
            typingSpeed = 100;
        }

        if (!isDeleting && charIndex === currentText.length) {
            setTimeout(() => {
                isDeleting = true;
            }, 2000);
        } else if (isDeleting && charIndex === 0) {
            isDeleting = false;
            textIndex = (textIndex + 1) % texts.length;
        }

        setTimeout(typeText, typingSpeed);
    }

    typeText();
}

// Scroll animations using Intersection Observer
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                
                // Trigger skill bars animation when skills section is visible
                if (entry.target.classList.contains('skills')) {
                    animateSkillBars();
                }
            }
        });
    }, observerOptions);

    // Observe all sections and elements that need animation
    const elementsToObserve = document.querySelectorAll('section, .about-text, .about-image, .skill-category, .project-card, .contact-item');
    elementsToObserve.forEach(el => {
        el.classList.add('fade-in');
        observer.observe(el);
    });
}

// Skill bars animation
function initSkillBars() {
    // This will be triggered by the intersection observer
}

function animateSkillBars() {
    const skillFills = document.querySelectorAll('.skill-fill');
    skillFills.forEach(fill => {
        const width = fill.getAttribute('data-width');
        setTimeout(() => {
            fill.style.width = width + '%';
        }, 300);
    });
}

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-link');
    const scrollArrow = document.querySelector('.scroll-arrow');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Scroll arrow functionality
    if (scrollArrow) {
        scrollArrow.addEventListener('click', () => {
            const aboutSection = document.querySelector('#about');
            if (aboutSection) {
                const offsetTop = aboutSection.offsetTop - 70;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    }
}

// Contact form functionality
function initContactForm() {
    const form = document.querySelector('.form');
    
    if (form) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(form);
            const name = form.querySelector('input[type="text"]').value;
            const email = form.querySelector('input[type="email"]').value;
            const message = form.querySelector('textarea').value;
            
            // Simple validation
            if (!name || !email || !message) {
                showNotification('Please fill in all fields', 'error');
                return;
            }
            
            if (!isValidEmail(email)) {
                showNotification('Please enter a valid email address', 'error');
                return;
            }
            
            // Simulate form submission
            showNotification('Message sent successfully! I\'ll get back to you soon.', 'success');
            form.reset();
        });
    }
}

// Email validation helper
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #00b894, #00a085)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #e17055, #d63031)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
    }
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Add some interactive effects
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effect to project cards
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Add parallax effect to hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroBackground = document.querySelector('.hero-background');
        if (heroBackground) {
            heroBackground.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
    
    // Add floating animation to shapes
    const shapes = document.querySelectorAll('.shape');
    shapes.forEach((shape, index) => {
        shape.style.animationDelay = `${index * 0.5}s`;
    });
});

// Cool Scroll Animations
function initCoolScrollAnimations() {
    // Create intersection observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all sections except hero (hero has its own animations)
    const sections = document.querySelectorAll('section:not(.hero)');
    sections.forEach(section => {
        observer.observe(section);
    });

    // Observe section headers
    const sectionHeaders = document.querySelectorAll('.section-header');
    sectionHeaders.forEach(header => {
        observer.observe(header);
    });

    // Observe section titles and subtitles
    const sectionTitles = document.querySelectorAll('.section-title');
    const sectionSubtitles = document.querySelectorAll('.section-subtitle');

    sectionTitles.forEach(title => observer.observe(title));
    sectionSubtitles.forEach(subtitle => observer.observe(subtitle));

    // Observe about content
    const aboutText = document.querySelector('.about-text');
    const aboutImage = document.querySelector('.about-image');
    if (aboutText) observer.observe(aboutText);
    if (aboutImage) observer.observe(aboutImage);

    // Observe skill categories
    const skillCategories = document.querySelectorAll('.skill-category');
    skillCategories.forEach(category => {
        observer.observe(category);
    });

    // Observe project cards
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        observer.observe(card);
    });

    // Observe contact content
    const contactInfo = document.querySelector('.contact-info');
    const contactForm = document.querySelector('.contact-form');
    const contactItems = document.querySelectorAll('.contact-item');

    if (contactInfo) observer.observe(contactInfo);
    if (contactForm) observer.observe(contactForm);
    contactItems.forEach(item => observer.observe(item));
}

// Viewport-Following Robot with Scroll Sync
function initParallaxEffect() {
    const parallaxElements = document.querySelectorAll('.parallax-element');
    const interactiveRobot = document.querySelector('.interactive-robot');
    const robotBody = document.querySelector('.robot-body');
    const speechBubble = document.querySelector('.robot-speech-bubble');
    const speechText = document.querySelector('.speech-text');
    const attentionPointer = document.querySelector('.robot-attention-pointer');
    const footprintsContainer = document.querySelector('.robot-footprints-container');

    let lastScrollY = window.pageYOffset;
    let robotPosition = { right: 50, top: 15 }; // Start at right side with fixed pixels
    let lastFootprintTime = 0;
    let speechTimer = null;

    // Speech messages
    const speechMessages = [
        "Hello! 👋", "How are you? 😊", "Nice portfolio! 🚀", "Keep scrolling! ⬇️",
        "Awesome work! ⭐", "I'm your guide! 🤖", "Looking good! 👍", "Tech is cool! 💻",
        "Great projects! 🎯", "Let's explore! 🔍", "Amazing skills! 💪", "You're talented! 🌟"
    ];

    function createFootprint(x, y) {
        if (!footprintsContainer) {
            console.log('Footprints container not found!');
            return;
        }

        const footprint = document.createElement('div');
        footprint.className = 'robot-footprint';

        // Calculate position relative to document (not viewport)
        const scrollTop = window.pageYOffset;
        const documentX = x - 8; // Center the larger paw print
        const documentY = y + scrollTop - 9; // Add scroll position to make it document-relative

        footprint.style.left = documentX + 'px';
        footprint.style.top = documentY + 'px';
        footprintsContainer.appendChild(footprint);

        console.log('Created footprint at document position:', documentX, documentY, 'Scroll:', scrollTop);

        // Remove footprint after 4 seconds (longer visibility)
        setTimeout(() => {
            if (footprint.parentNode) {
                footprint.parentNode.removeChild(footprint);
            }
        }, 4000);
    }

    function initializeRobot() {
        // Robot appears 100% visible at right side of viewport
        robotPosition = { right: 50, top: 15 };
        interactiveRobot.style.right = `${robotPosition.right}px`;
        interactiveRobot.style.top = `${robotPosition.top}%`;
        interactiveRobot.style.opacity = '1';
        interactiveRobot.style.visibility = 'visible';

        console.log('Robot initialized at:', robotPosition);

        // Create initial footprint
        setTimeout(() => {
            const robotRect = interactiveRobot.getBoundingClientRect();
            createFootprint(
                robotRect.left + robotRect.width / 2,
                robotRect.top + robotRect.height / 2
            );
        }, 500);
    }

    function handleRightSideMovement() {
        // Keep robot on right side with slight variations
        const rightSideBase = 50; // Base right position in pixels
        const variation = Math.sin(Date.now() * 0.0005) * 15; // Gentle side movement ±15px

        robotPosition.right = rightSideBase + variation;

        // Ensure robot stays within right margin (30-80px from right)
        robotPosition.right = Math.max(30, Math.min(80, robotPosition.right));
    }

    function showSpeechBubble() {
        const randomMessage = speechMessages[Math.floor(Math.random() * speechMessages.length)];
        speechText.textContent = randomMessage;
        speechBubble.classList.add('show');

        setTimeout(() => {
            speechBubble.classList.remove('show');
        }, 3000);

        // Schedule next speech bubble (10-15 seconds)
        const nextSpeechDelay = 10000 + Math.random() * 5000;
        speechTimer = setTimeout(showSpeechBubble, nextSpeechDelay);
    }

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const scrollDirection = scrolled > lastScrollY ? 'down' : 'up';
        const scrollSpeed = Math.abs(scrolled - lastScrollY);

        // Original parallax elements
        parallaxElements.forEach((element, index) => {
            const speed = 0.3 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });

        // Robot scroll behavior - much slower and always visible
        if (interactiveRobot) {
            const currentTime = Date.now();

            // Reset robot to top if user is at the very top of page
            if (scrolled < 100) {
                robotPosition.top = 15; // Reset to top position
            }

            // Robot follows scroll very gradually
            if (scrollSpeed > 1) {
                // Much smaller movement - robot moves very slowly
                const smallMovement = scrollSpeed * 0.05; // Only 5% of scroll speed

                if (scrollDirection === 'down') {
                    robotPosition.top += smallMovement;
                } else {
                    robotPosition.top -= smallMovement;
                }

                // Keep robot always visible in viewport (15% to 75%)
                robotPosition.top = Math.max(15, Math.min(75, robotPosition.top));

                console.log('Robot position:', robotPosition.top, 'Small movement:', smallMovement, 'Scroll speed:', scrollSpeed);

                // Create footprints during movement
                if (currentTime - lastFootprintTime > 200) {
                    const robotRect = interactiveRobot.getBoundingClientRect();
                    createFootprint(
                        robotRect.left + robotRect.width / 2,
                        robotRect.top + robotRect.height / 2
                    );
                    lastFootprintTime = currentTime;
                }
            }

            // Apply positions
            interactiveRobot.style.top = `${robotPosition.top}%`;
            interactiveRobot.style.right = `${robotPosition.right}px`;

            // Robot personality based on scroll behavior
            if (scrollSpeed > 20) {
                robotBody.style.animation = 'robotExcited 0.3s ease-in-out infinite';
            } else if (scrollSpeed > 5) {
                robotBody.style.animation = 'robotBreathe 1.5s ease-in-out infinite';
            } else {
                robotBody.style.animation = 'robotBreathe 3s ease-in-out infinite';
            }

            // Change color based on scroll direction
            if (scrollDirection === 'down') {
                robotBody.style.color = '#00ffff';
                robotBody.style.textShadow = '0 0 20px rgba(0, 255, 255, 0.8), 0 0 40px rgba(0, 255, 255, 0.6)';
            } else if (scrollDirection === 'up') {
                robotBody.style.color = '#ff1493';
                robotBody.style.textShadow = '0 0 20px rgba(255, 20, 147, 0.8), 0 0 40px rgba(255, 20, 147, 0.6)';
            }
        }

        lastScrollY = scrolled;
    });

    // Animation loop for right-side movement
    function animateRobot() {
        handleRightSideMovement();

        // Apply horizontal position (using right instead of left)
        interactiveRobot.style.right = `${robotPosition.right}px`;

        requestAnimationFrame(animateRobot);
    }

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Robot interactions
    if (robotBody) {
        robotBody.addEventListener('click', () => {
            scrollToNextSection();
            robotBody.style.animation = 'robotExcited 0.5s ease-in-out 3';

            // Show excited speech
            speechText.textContent = "Let's go! 🚀";
            speechBubble.classList.add('show');
            setTimeout(() => {
                speechBubble.classList.remove('show');
                robotBody.style.animation = 'robotBreathe 2s ease-in-out infinite';
            }, 2000);
        });

        robotBody.addEventListener('mouseenter', () => {
            robotBody.style.transform = 'scale(1.2)';
            attentionPointer.style.opacity = '1';
        });

        robotBody.addEventListener('mouseleave', () => {
            robotBody.style.transform = 'scale(1)';
            attentionPointer.style.opacity = '0';
        });
    }

    // Initialize robot and start systems
    initializeRobot();
    animateRobot();
    setTimeout(showSpeechBubble, 5000); // First message after 5 seconds
}

// Helper function to determine current section
function getCurrentSection(scrollPercent) {
    if (scrollPercent < 0.2) return 'hero';
    if (scrollPercent < 0.4) return 'about';
    if (scrollPercent < 0.6) return 'skills';
    if (scrollPercent < 0.8) return 'projects';
    return 'contact';
}

// Helper function to scroll to next important section
function scrollToNextSection() {
    const sections = ['#about', '#skills', '#projects', '#contact'];
    const currentScroll = window.pageYOffset;

    for (let section of sections) {
        const element = document.querySelector(section);
        if (element && element.offsetTop > currentScroll + 100) {
            element.scrollIntoView({ behavior: 'smooth' });
            break;
        }
    }

    // If at the end, go back to top
    if (currentScroll > document.documentElement.scrollHeight - window.innerHeight - 200) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}



// Console welcome message
console.log('%c🚀 Welcome to my portfolio!', 'color: #00ffff; font-size: 20px; font-weight: bold;');
console.log('%cFeel free to explore the code and reach out if you have any questions!', 'color: #ff1493; font-size: 14px;');
